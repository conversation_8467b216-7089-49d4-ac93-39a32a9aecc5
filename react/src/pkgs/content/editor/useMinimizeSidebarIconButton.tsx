import { colours } from '@/common/colours'
import CustomIconButton from '@/common/components/CustomIconButton'
import { useCallback, useEffect, useState } from 'react'
import FirstPageIcon from '@mui/icons-material/FirstPage'
import LastPage from '@mui/icons-material/LastPage'
import { useStateWithStorage } from '@/common/storage.service'

interface MinimizeSidebarIconButtonProps {
    anchorElement: HTMLElement | undefined | null
}

export default function useMinimizeSidebarIconButton({ anchorElement }: MinimizeSidebarIconButtonProps) {
    const [sidebarIsMinimized, setSidebarIsMinimized] = useStateWithStorage(
        'content-editor-sidebar-is-minimized',
        false
    )

    const [buttonLeftPosition, setButtonLeftPosition] = useState('0px')

    // Update buttonLeftPosition whenever anchorElement or sidebarIsMinimized changes
    useEffect(() => {
        const updateButtonLeftPosition = () => {
            const leftPositionPx = (anchorElement?.getBoundingClientRect().right || 0) - 8
            setButtonLeftPosition(`${leftPositionPx}px`)
        }

        updateButtonLeftPosition()

        // Add resize listener to handle window resize events
        const handleResize = () => {
            updateButtonLeftPosition()
        }

        window.addEventListener('resize', handleResize)

        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [anchorElement, sidebarIsMinimized])

    const MinimizeSidebarIconButton = useCallback(() => {
        return (
            <CustomIconButton
                size='small'
                round
                onClick={() => setSidebarIsMinimized(!sidebarIsMinimized)}
                sx={{
                    fontSize: '4px',
                    backgroundColor: colours.base_blue,
                    color: colours.white,
                    border: `1px solid ${colours.off_white_but_darker}`,
                    zIndex: 1,
                    position: 'fixed',
                    marginTop: `160px`,
                    left: buttonLeftPosition,
                    '.MuiSvgIcon-root': {
                        fontSize: '18px'
                    }
                }}
            >
                {sidebarIsMinimized ? <FirstPageIcon /> : <LastPage />}
            </CustomIconButton>
        )
    }, [buttonLeftPosition, setSidebarIsMinimized, sidebarIsMinimized])

    return { sidebarIsMinimized, MinimizeSidebarIconButton }
}
